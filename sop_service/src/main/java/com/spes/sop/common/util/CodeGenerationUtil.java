package com.spes.sop.common.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 编码生成工具类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CodeGenerationUtil {
    /**
     * SPU编码前缀
     */
    private static final String SPU_PREFIX = "SPES";

    /**
     * SKU编码前缀
     */
    private static final String SKU_PREFIX = "SKU";

    /**
     * 组合编码前缀
     */
    private static final String COMBO_PREFIX = "ZH";


    /**
     * 生成SPU编码
     *
     * @return SPU编码
     */
    public static String generateSpuCode() {
        return SPU_PREFIX + generateCode();
    }


    /**
     * 生成货品编码
     *
     * @param specCode 规格代号
     * @return 货品编码
     */
    public static String generateSkuCode(Long specCode) {
        return SKU_PREFIX + generateCode() + "-" + specCode;
    }

    /**
     * 生成组合编码
     * 编码规则：ZH2025040001
     *
     * @return 组合编码
     */
    public static String generateComboCode() {
        return COMBO_PREFIX + generateCode();
    }


    /**
     * 根据时间生成不重复的编码(再通过一位随机数实现唯一性)
     */
    private static String generateCode() {
        String time = new SimpleDateFormat("yyMMddHHmmssSSS").format(new Date());
        return time + (int) (Math.random() * 10);
    }
} 