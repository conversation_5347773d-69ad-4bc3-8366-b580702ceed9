package com.spes.sop.common.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

/**
 * 审核状态枚举
 */
public enum GoodsStatusEnum {

    CREATED("已创建"),
    APPROVING("审批中"),
    APPROVED("审批通过"),
    REJECTED("审批未通过"),
    CANCELED("已取消"),
    SYNC_FAILED("同步失败");

    @Getter
    private final String desc;

    GoodsStatusEnum(String desc) {
        this.desc = desc;
    }

    public static GoodsStatusEnum getByName(String code) {
        if (ObjectUtil.isNull(code)) {
            return null;
        }
        for (GoodsStatusEnum value : values()) {
            if (value.name().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
