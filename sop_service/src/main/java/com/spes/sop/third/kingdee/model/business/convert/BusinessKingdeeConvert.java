package com.spes.sop.third.kingdee.model.business.convert;

import com.spes.sop.third.kingdee.model.business.request.MaterialSaveReq;

import java.util.HashMap;
import java.util.Map;

public interface BusinessKingdeeConvert {
    /**
     * 组装金蝶物料保存的请求体
     *
     * @return 组装好的 Map 结构
     */
    static Map<String, Object> buildKingdeeMaterialPayload(MaterialSaveReq req) {

        // 1. 创建核心的 Model 部分
        Map<String, Object> model = new HashMap<>();
        model.put("FMATERIALID", 0);
        //  组织信息
        model.put("FCreateOrgId", createFNumberMap("001"));
        model.put("FUseOrgId", createFNumberMap("001"));

        // sku 基本信息
        model.put("FNumber", req.getSkuCode());
        model.put("FName", req.getSkuName());
        model.put("FSpecification", req.getSpecification());
        model.put("FMaterialGroup", ObjectcreateFNumberMap(req.getMaterialGroup().name()));

        model.put("FDSMatchByLot", false);
        model.put("FImgStorageType", "B");
        model.put("FIsSalseByNet", false);
        model.put("FIsHandleReserve", true);
        model.put("FISSYNC", Boolean.TRUE);

        // 2. 组装所有 SubHeadEntity 子对象
        model.put("SubHeadEntity", buildSubHeadEntity(req));
        model.put("SubHeadEntity1", buildSubHeadEntity1());
        model.put("SubHeadEntity2", buildSubHeadEntity2());
        model.put("SubHeadEntity3", buildSubHeadEntity3());
        model.put("SubHeadEntity4", buildSubHeadEntity4());
        model.put("SubHeadEntity5", buildSubHeadEntity5());
        model.put("SubHeadEntity6", buildSubHeadEntity6());
        model.put("SubHeadEntity7", buildSubHeadEntity7());

        return model;
    }

    // --- 辅助方法，用于创建各个子对象 ---

    static Map<String, Object> buildSubHeadEntity(MaterialSaveReq req) {
        Map<String, Object> map = new HashMap<>();
        map.put("FErpClsID", req.getMaterialAttribute().getCode());
        map.put("FIsSubContract", req.getMaterialAttribute().getSubContract());
        map.put("FFeatureItem", "1");
        map.put("FCategoryID", createFNumberMap(req.getInventoryType().getCode()));
        map.put("FTaxType", createFNumberMap("WLDSFL01_SYS"));
        map.put("FBARCODE", req.getBarCode());
        map.put("FTaxRateId", createFNumberMapWithUpperKey("SL02_SYS")); // 注意这个的Key是FNUMBER
        map.put("FBaseUnitId", createFNumberMap("Pcs"));
        map.put("FIsPurchase", true);
        map.put("FIsInventory", true);
        map.put("FIsSale", true);
        map.put("FIsProduce", false);
        map.put("FIsAsset", false);
        map.put("FGROSSWEIGHT", 0.0);
        map.put("FNETWEIGHT", 0.0);
        map.put("FWEIGHTUNITID", createFNumberMapWithUpperKey("kg"));
        map.put("FLENGTH", 0.0);
        map.put("FWIDTH", 0.0);
        map.put("FHEIGHT", 0.0);
        map.put("FVOLUME", 0.0);
        map.put("FVOLUMEUNITID", createFNumberMapWithUpperKey("m"));
        map.put("FSuite", "0");
        map.put("FCostPriceRate", 0.0);
        return map;
    }

    static Map<String, Object> buildSubHeadEntity1() {
        Map<String, Object> map = new HashMap<>();
        map.put("FStoreUnitID", createFNumberMap("Pcs"));
        map.put("FUnitConvertDir", "1");
        map.put("FIsLockStock", true);
        return map;
    }

    static Map<String, Object> buildSubHeadEntity2() {
        Map<String, Object> map = new HashMap<>();
        map.put("FSaleUnitId", createFNumberMap("Pcs"));
        map.put("FSalePriceUnitId", createFNumberMap("Pcs"));
        map.put("FIsReturn", true);
        return map;
    }

    static Map<String, Object> buildSubHeadEntity3() {
        Map<String, Object> map = new HashMap<>();
        map.put("FPurchaseUnitId", createFNumberMap("Pcs"));
        map.put("FPurchasePriceUnitId", createFNumberMap("Pcs"));
        map.put("FQuotaType", "1");
        return map;
    }

    static Map<String, Object> buildSubHeadEntity4() {
        Map<String, Object> map = new HashMap<>();
        map.put("FPlanningStrategy", "1");
        map.put("FOrderPolicy", "0");
        map.put("FFixLeadTimeType", "1");
        map.put("FVarLeadTimeType", "1");
        map.put("FCheckLeadTimeType", "1");
        map.put("FOrderIntervalTimeType", "3");
        map.put("FReserveType", "1");
        map.put("FPlanOffsetTimeType", "1");
        return map;
    }

    static Map<String, Object> buildSubHeadEntity5() {
        Map<String, Object> map = new HashMap<>();
        map.put("FIssueType", "1");
        map.put("FOverControlMode", "1");
        map.put("FMinIssueUnitId", createFNumberMapWithUpperKey("Pcs"));
        map.put("FStandHourUnitId", "3600");
        map.put("FBackFlushType", "1");
        return map;
    }

    static Map<String, Object> buildSubHeadEntity6() {
        Map<String, Object> map = new HashMap<>();
        map.put("FFirstQCControlType", "0");
        return map;
    }

    static Map<String, Object> buildSubHeadEntity7() {
        Map<String, Object> map = new HashMap<>();
        map.put("FSubconUnitId", createFNumberMap("Pcs"));
        map.put("FSubconPriceUnitId", createFNumberMap("Pcs"));
        map.put("FSubBillType", createFNumberMapWithUpperKey("WWDD01_SYS"));
        return map;
    }

    /**
     * 辅助方法，用于创建 {"FNumber": "value"} 格式的Map
     */
    static Map<String, String> createFNumberMap(String value) {
        Map<String, String> map = new HashMap<>();
        map.put("FNumber", value);
        return map;
    }

    /**
     * 辅助方法，用于创建 {"FNUMBER": "value"} 格式的Map (注意Key是大写)
     */
    static Map<String, String> createFNumberMapWithUpperKey(String value) {
        Map<String, String> map = new HashMap<>();
        map.put("FNUMBER", value);
        return map;
    }
}
