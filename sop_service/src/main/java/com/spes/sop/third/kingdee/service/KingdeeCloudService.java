package com.spes.sop.third.kingdee.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.spes.sop.third.kingdee.client.K3CloudTemplate;
import com.spes.sop.third.kingdee.config.KingdeeConfig;
import com.spes.sop.third.kingdee.model.base.*;
import com.spes.sop.third.kingdee.model.business.convert.BusinessKingdeeConvert;
import com.spes.sop.third.kingdee.model.business.entity.KingdeeCustomerBO;
import com.spes.sop.third.kingdee.model.business.request.MaterialSaveReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class KingdeeCloudService {

    private final Authentication auth;
    private final K3CloudTemplate template;

    /**
     * 金蝶物料
     */
    private static final String MATERIAL_FORM_ID = "BD_MATERIAL";

    public KingdeeCloudService(KingdeeConfig kingdeeConfig) {
        this.auth = new Authentication(kingdeeConfig.getAcctId(),
                kingdeeConfig.getUsername(), kingdeeConfig.getPassword());
        this.template = new K3CloudTemplate(kingdeeConfig.getServerUrl(), auth);
    }

    public void saveMaterial(MaterialSaveReq req) {
        if (ObjectUtil.isNull(req)) {
            return;
        }
        log.info("开始保存金蝶物料...");
        BillSave billSave = new BillSave(MATERIAL_FORM_ID);
        billSave.setModel(BusinessKingdeeConvert.buildKingdeeMaterialPayload(req));
        Result result = template.executeBillSave(billSave, Result.class);
        log.info("金蝶物料保存结果:{}", result);
    }

    public List<KingdeeCustomerBO> getCustomerInfo(String name) {
        // 查询字段
        String[] fieldKeys = {
                "FNumber", "FName"
        };
        // 过滤条件, 具体格式可以参考金蝶文档, 比如:"FBOOKID=100453"
        String filter = "FName='" + name + "'";
        BillQuery query = new BillQuery("bd_customer", StringUtils.arrayToDelimitedString(fieldKeys, ","), filter);
        query.setStartRow(0);
        // 0代表无限制
        query.setLimit(100);
        List<KingdeeCustomerBO> dataList = template.executeBillQuery(query, KingdeeCustomerBO.class);
        log.info("获取客户信息 = {}", JSONUtil.toJsonStr(dataList));
        return dataList;
    }

    public void submitBill(String code) {
        log.info("开始提交金蝶单据...");
        BillSubmit<String> billSubmit = new BillSubmit<>(MATERIAL_FORM_ID);
        billSubmit.setNumbers(Lists.newArrayList(code));
        Result result = template.executeBillSubmit(billSubmit, Result.class);
        log.info("金蝶单据提交结果:{}", result);
    }

    public void auditBill(String code) {
        log.info("开始审核金蝶单据...");
        BillAudit<String> billSubmit = new BillAudit<>(MATERIAL_FORM_ID);
        billSubmit.setNumbers(Lists.newArrayList(code));
        Result result = template.executeBillAudit(billSubmit, Result.class);
        log.info("金蝶单据审核结果:{}", result);
    }

    /**
     * 金蝶返回结果
     */
    public static class Result {
        //{
        //    "Result": {
        //        "ResponseStatus": {
        //            "ErrorCode": 500,
        //            "IsSuccess": false,
        //            "Errors": [
        //                {
        //                    "FieldName": "",
        //                    "Message": "编码为“SKU2506201421225696-2324”的物料，已审核的单据不允许提交!",
        //                    "DIndex": 0
        //                }
        //            ],
        //            "SuccessEntitys": [],
        //            "SuccessMessages": [],
        //            "MsgCode": 11
        //        }
        //    }
        //}
        private ResultStatus responseStatus;
    }
    public static class ResultStatus {
        private int errorCode;
        private boolean isSuccess;
        private List<KingdeeError> errors;
        private int msgCode;
    }

    public static class KingdeeError {
        private String fieldName;
        private String message;
        private int dIndex;
    }
} 