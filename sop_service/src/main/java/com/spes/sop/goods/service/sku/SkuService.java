package com.spes.sop.goods.service.sku;

import com.spes.sop.goods.service.sku.model.bo.SkuDetailBO;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.sku.model.command.SkuCreateCommand;
import com.spes.sop.goods.service.sku.model.command.SkuUpdateCommand;
import com.spes.sop.goods.service.sku.model.query.SkuBOListQuery;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * SKU服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface SkuService {

    /**
     * 列表查询
     *
     * @param query 查询条件
     * @return SKU列表
     */
    List<SkuListBO> list(SkuBOListQuery query);

    /**
     * 查询SKU总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Long count(SkuBOListQuery query);

    /**
     * 根据ID查询SKU详情
     *
     * @param id SKU ID，不能为空
     * @return SKU详情，如果不存在则返回null
     */
    SkuDetailBO getById(@NotNull Long id);

    /**
     * 创建SKU
     *
     * @param addReq 新增请求
     * @return 创建成功的SKU ID
     */
    Long create(@NotNull SkuCreateCommand addReq);

    /**
     * 更新SKU
     *
     * @param command 更新请求
     * @return 是否更新成功
     */
    boolean update(@NotNull SkuUpdateCommand command);

    /**
     * 删除SKU（逻辑删除）
     *
     * @param id        SKU ID
     * @param updaterId 更新人ID
     * @return 是否删除成功
     */
    boolean delete(@NotNull Long id, @NotNull Long updaterId);
}
