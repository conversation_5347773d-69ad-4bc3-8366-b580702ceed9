package com.spes.sop.goods.service.combination.model.query;

import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.common.page.BasePager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 组合品分页查询（Service层）
 *
 * <AUTHOR>

 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CombinationBOPageQuery extends BasePager {

    /**
     * 组合品名称（模糊查询）
     */
    private String combinationNameSearch;

    /**
     * 组合品编码（精确查询）
     */
    private String combinationCode;

    /**
     * 一级分类
     */
    private List<String> requestIds;

    /**
     * 一级分类
     */
    private List<Long> firstClassification;

    /**
     * 二级分类
     */
    private List<Long> secondClassification;

    /**
     * 三级分类
     */
    private List<Long> thirdClassification;

    /**
     * 系列
     */
    private Long seriesId;

    /**
     * 产品分类
     */
    private List<Long> categoryIds;

    /**
     * 状态
     */
    private List<GoodsStatusEnum> statuses;

    /**
     * 是否赠品
     */
    private Boolean gift;
    /**
     * 是否为包裹卡
     */
    private Boolean packageCard;
} 
