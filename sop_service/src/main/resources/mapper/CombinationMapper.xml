<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.spes.sop.goods.mapper.CombinationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.spes.sop.goods.mapper.model.entity.CombinationDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="combination_code" property="combinationCode" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="brand_id" property="brandId" jdbcType="BIGINT"/>
        <result column="first_classification" property="firstClassification" jdbcType="BIGINT"/>
        <result column="second_classification" property="secondClassification" jdbcType="BIGINT"/>
        <result column="third_classification" property="thirdClassification" jdbcType="BIGINT"/>
        <result column="fourth_classification" property="fourthClassification" jdbcType="INTEGER"/>
        <result column="series_id" property="seriesId" jdbcType="BIGINT"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="channel_id" property="channelId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="oa_id" property="oaId" jdbcType="BIGINT"/>
        <result column="modified_oa_status" property="modifiedOaStatus" jdbcType="VARCHAR"/>
        <result column="modified_oa_id" property="modifiedOaId" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="updater_id" property="updaterId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id
        , combination_code, name, description, brand_id,
        first_classification, second_classification, third_classification, fourth_classification,
        series_id, category_id, channel_id, status, oa_id, modified_oa_status, modified_oa_id, version, creator_id, updater_id, create_time, update_time, deleted
    </sql>

    <!-- 查询条件 -->
    <sql id="queryCondition">
        <where>
            <if test="query.ids != null and query.ids.size() > 0">
                AND id IN
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.combinationCodes != null and query.combinationCodes.size() > 0">
                AND combination_code IN
                <foreach collection="query.combinationCodes" item="combinationCode" open="(" separator="," close=")">
                    #{combinationCode}
                </foreach>
            </if>
            <if test="query.oaIds != null and query.oaIds.size() > 0">
                AND oa_id IN
                <foreach collection="query.oaIds" item="odId" open="(" separator="," close=")">
                    #{odId}
                </foreach>
            </if>
            <if test="query.firstClassifications != null and query.firstClassifications.size() > 0">
                AND first_classification IN
                <foreach collection="query.firstClassifications" item="firstClassification" open="(" separator=","
                         close=")">
                    #{firstClassification}
                </foreach>
            </if>
            <if test="query.secondClassifications != null and query.secondClassifications.size() > 0">
                AND second_classification IN
                <foreach collection="query.secondClassifications" item="secondClassification" open="(" separator=","
                         close=")">
                    #{secondClassification}
                </foreach>
            </if>
            <if test="query.thirdClassifications != null and query.thirdClassifications.size() > 0">
                AND third_classification IN
                <foreach collection="query.thirdClassifications" item="thirdClassification" open="(" separator=","
                         close=")">
                    #{thirdClassification}
                </foreach>
            </if>
            <if test="query.seriesId != null">
                AND series_id like CONCAT('%', #{query.seriesId}, '%')
            </if>
            <if test="query.categoryIds != null and query.categoryIds.size() > 0">
                AND category_id IN
                <foreach collection="query.categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="query.statuses != null and query.statuses.size() > 0">
                AND status IN
                <foreach collection="query.statuses" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query.modifiedOaStatuses != null and query.modifiedOaStatuses.size() > 0">
                AND modified_oa_status IN
                <foreach collection="query.modifiedOaStatuses" item="modifiedOaStatus" open="(" separator="," close=")">
                    #{modifiedOaStatus}
                </foreach>
            </if>
            <if test="query.gift != null">
                AND gift = #{query.gift}
            </if>
            <if test="query.packageCard != null">
                AND package_card = #{query.packageCard}
            </if>
            <if test="query.nameSearch != null and query.nameSearch  != ''">
                AND combination_code LIKE CONCAT('%', #{query.nameSearch}, '%')
            </if>
            <if test="true">
                AND deleted = 0
            </if>
        </where>
    </sql>


    <!-- 通用排序 -->
    <sql id="Common_Order_By">
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ORDER BY ${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
    </sql>


    <!-- 分页查询组合品列表 -->
    <select id="list" parameterType="com.spes.sop.goods.mapper.model.query.CombinationDOListQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_combination
        <include refid="queryCondition"/>
        <include refid="Common_Order_By"/>

        <if test="query.offset != null and query.limit != null">
            LIMIT #{query.offset}, #{query.limit}
        </if>

    </select>

    <!-- 查询组合品总数 -->
    <select id="count" parameterType="com.spes.sop.goods.mapper.model.query.CombinationDOListQuery"
            resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sop_combination
        <include refid="queryCondition"/>
    </select>

    <!-- 查询单个组合品 -->
    <select id="getCombination" parameterType="com.spes.sop.goods.mapper.model.query.CombinationDOGetQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sop_combination
        <where>

            <if test="query.id != null">
                AND id = #{query.id}
            </if>
            <if test="query.combinationCode != null">
                AND combination_code = #{query.combinationCode}
            </if>
            <if test="query.name != null">
                AND name = #{query.name}
            </if>
            <if test="true">
                AND deleted = 0
            </if>
        </where>
        LIMIT 1
    </select>

    <!-- 新增组合品 -->
    <insert id="insert" parameterType="com.spes.sop.goods.mapper.model.req.CombinationAddReq"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sop_combination (id,
                                     combination_code,
                                     name,
                                     description,
                                     brand_id,
                                     first_classification,
                                     second_classification,
                                     third_classification,
                                     fourth_classification,
                                     series_id,
                                     status,
                                     oa_id,
                                     modified_oa_status,
                                     modified_oa_id,
                                     version,
                                     channel_id,
                                     category_id,
                                     creator_id,
                                     updater_id,
                                     create_time,
                                     update_time,
                                     deleted)
        VALUES (#{req.id},
                #{req.combinationCode},
                #{req.name},
                #{req.description},
                #{req.brandId},
                #{req.firstClassification},
                #{req.secondClassification},
                #{req.thirdClassification},
                #{req.fourthClassification},
                #{req.seriesId},
                #{req.status},
                #{req.oaId},
                #{req.modifiedOaStatus},
                #{req.modifiedOaId},
                COALESCE(#{req.version}, 1),
                #{req.channelId},
                #{req.categoryId},
                #{req.creatorId},
                #{req.updaterId},
                NOW(),
                NOW(),
                0)
    </insert>

    <!-- 批量新增组合品 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sop_combination (
        id,
        combination_code,
        name,
        description,
        brand_id,
        first_classification,
        second_classification,
        third_classification,
        fourth_classification,
        series_id,
        category_id,
        status,
        oa_id,
        modified_oa_status,
        modified_oa_id,
        version,
        channel_id ,
        creator_id,
        updater_id,
        create_time,
        update_time,
        deleted
        ) VALUES
        <foreach collection="req" item="item" separator=",">
            (
            #{item.id},
            #{item.combinationCode},
            #{item.name},
            #{item.description},
            #{item.brandId},
            #{item.firstClassification},
            #{item.secondClassification},
            #{item.thirdClassification},
            #{item.fourthClassification},
            #{item.seriesId},
            #{item.categoryId},
            #{item.status},
            #{item.oaId},
            #{item.modifiedOaStatus},
            #{item.modifiedOaId},
            COALESCE(#{item.version}, 1),
            #{item.channelId},
            #{item.creatorId},
            #{item.updaterId},
            NOW(),
            NOW(),
            0
            )
        </foreach>
    </insert>

    <!-- 更新组合品 -->
    <update id="updateById" parameterType="com.spes.sop.goods.mapper.model.req.CombinationUpdateReq">
        UPDATE sop_combination
        <set>
            <if test="req.combinationCode != null">
                combination_code = #{req.combinationCode},
            </if>
            <if test="req.name != null">
                name = #{req.name},
            </if>
            <if test="req.description != null">
                description = #{req.description},
            </if>
            <if test="req.brandId != null">
                brand_id = #{req.brandId},
            </if>
            <if test="req.firstClassification != null">
                first_classification = #{req.firstClassification},
            </if>
            <if test="req.secondClassification != null">
                second_classification = #{req.secondClassification},
            </if>
            <if test="req.thirdClassification != null">
                third_classification = #{req.thirdClassification},
            </if>
            <if test="req.fourthClassification != null">
                fourth_classification = #{req.fourthClassification},
            </if>
            <if test="req.seriesId != null">
                series_id = #{req.seriesId},
            </if>
            <if test="req.categoryId != null">
                category_id = #{req.categoryId},
            </if>
            <if test="req.status != null">
                status = #{req.status},
            </if>
            <if test="req.oaId != null">
                oa_id = #{req.oaId},
            </if>
            <if test="req.modifiedOaStatus != null">
                modified_oa_status = #{req.modifiedOaStatus},
            </if>
            <if test="req.modifiedOaId != null">
                modified_oa_id = #{req.modifiedOaId},
            </if>
            <if test="req.version != null">
                version = #{req.version},
            </if>
            <if test="req.channelId != null">
                channel_id = #{req.channelId},
            </if>
            <if test="req.updaterId != null">
                updater_id = #{req.updaterId},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{req.id} AND deleted = 0
    </update>

    <!-- 逻辑删除组合品 -->
    <update id="delete" parameterType="com.spes.sop.goods.mapper.model.req.CombinationDeleteReq">
        UPDATE sop_combination
        SET deleted     = 1,
            updater_id = #{req.updaterId},
            update_time = NOW()
        WHERE id = #{req.id}
          AND deleted = 0
    </update>

    <!-- 根据编码前缀统计组合品数量 -->
    <select id="countByCodePrefix" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM sop_combination
        WHERE combination_code LIKE #{codePrefix}
          AND deleted = 0
    </select>

</mapper> 