import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 简单的JSON反序列化测试
 * 用于验证金蝶返回结果的修复是否有效
 */
public class JsonTestRunner {
    
    public static void main(String[] args) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            
            // 模拟金蝶实际返回的JSON响应
            String jsonResponse = """
                {
                    "Result": {
                        "ResponseStatus": {
                            "ErrorCode": 500,
                            "IsSuccess": false,
                            "Errors": [
                                {
                                    "FieldName": "FNumber",
                                    "Message": "系统中已存在相同编码SKU2506201421225696-2324的物料，保存失败，请修改。",
                                    "DIndex": 0
                                }
                            ],
                            "SuccessEntitys": [],
                            "SuccessMessages": [],
                            "MsgCode": 11
                        },
                        "Id": "",
                        "Number": ""
                    }
                }
                """;

            // 执行反序列化
            KingdeeResult result = objectMapper.readValue(jsonResponse, KingdeeResult.class);
            
            System.out.println("✅ JSON反序列化成功！");
            System.out.println("错误码: " + result.getResult().getResponseStatus().getErrorCode());
            System.out.println("是否成功: " + result.getResult().getResponseStatus().getIsSuccess());
            System.out.println("错误消息: " + result.getResult().getResponseStatus().getErrors().get(0).getMessage());
            
        } catch (Exception e) {
            System.err.println("❌ JSON反序列化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 复制修复后的类结构
    public static class KingdeeResult {
        @JsonProperty("Result")
        private Result result;
        
        public Result getResult() { return result; }
        public void setResult(Result result) { this.result = result; }
    }
    
    public static class Result {
        @JsonProperty("ResponseStatus")
        private ResultStatus responseStatus;
        
        @JsonProperty("Id")
        private String id;
        
        @JsonProperty("Number")
        private String number;
        
        public ResultStatus getResponseStatus() { return responseStatus; }
        public void setResponseStatus(ResultStatus responseStatus) { this.responseStatus = responseStatus; }
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getNumber() { return number; }
        public void setNumber(String number) { this.number = number; }
    }
    
    public static class ResultStatus {
        @JsonProperty("ErrorCode")
        private Integer errorCode;
        
        @JsonProperty("IsSuccess")
        private Boolean isSuccess;
        
        @JsonProperty("Errors")
        private java.util.List<KingdeeError> errors;
        
        @JsonProperty("SuccessEntitys")
        private java.util.List<Object> successEntitys;
        
        @JsonProperty("SuccessMessages")
        private java.util.List<String> successMessages;
        
        @JsonProperty("MsgCode")
        private Integer msgCode;
        
        public Integer getErrorCode() { return errorCode; }
        public void setErrorCode(Integer errorCode) { this.errorCode = errorCode; }
        public Boolean getIsSuccess() { return isSuccess; }
        public void setIsSuccess(Boolean isSuccess) { this.isSuccess = isSuccess; }
        public java.util.List<KingdeeError> getErrors() { return errors; }
        public void setErrors(java.util.List<KingdeeError> errors) { this.errors = errors; }
        public java.util.List<Object> getSuccessEntitys() { return successEntitys; }
        public void setSuccessEntitys(java.util.List<Object> successEntitys) { this.successEntitys = successEntitys; }
        public java.util.List<String> getSuccessMessages() { return successMessages; }
        public void setSuccessMessages(java.util.List<String> successMessages) { this.successMessages = successMessages; }
        public Integer getMsgCode() { return msgCode; }
        public void setMsgCode(Integer msgCode) { this.msgCode = msgCode; }
    }
    
    public static class KingdeeError {
        @JsonProperty("FieldName")
        private String fieldName;
        
        @JsonProperty("Message")
        private String message;
        
        @JsonProperty("DIndex")
        private Integer dIndex;
        
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Integer getDIndex() { return dIndex; }
        public void setDIndex(Integer dIndex) { this.dIndex = dIndex; }
    }
}
