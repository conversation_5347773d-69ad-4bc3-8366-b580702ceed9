package com.spes.sop.support.goods.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.spes.sop.common.enums.GoodsStatusEnum;
import com.spes.sop.common.page.BasePager;
import com.spes.sop.common.security.JwtUserDetails;
import com.spes.sop.config.service.model.bo.*;
import com.spes.sop.controller.goods.model.query.CombinationPageQuery;
import com.spes.sop.controller.goods.model.request.CombinationCreateRequest;
import com.spes.sop.controller.goods.model.request.CombinationUpdateRequest;
import com.spes.sop.goods.service.combination.model.bo.CombinationDetailBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationListBO;
import com.spes.sop.goods.service.combination.model.bo.CombinationSkuRelationBO;
import com.spes.sop.goods.service.combination.model.command.CombinationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationSkuRelationCreateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationSkuRelationUpdateCommand;
import com.spes.sop.goods.service.combination.model.command.CombinationUpdateCommand;
import com.spes.sop.goods.service.combination.model.query.CombinationBOPageQuery;
import com.spes.sop.goods.service.sku.model.bo.SkuListBO;
import com.spes.sop.goods.service.spu.model.bo.SpuBO;
import com.spes.sop.support.goods.model.dto.CombinationDetailDTO;
import com.spes.sop.support.goods.model.dto.CombinationPageDTO;
import com.spes.sop.support.goods.model.dto.CombinationSkuItemDTO;
import com.spes.sop.third.weaver.constants.WeaverWorkflowConstant;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTable;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTableField;
import com.spes.sop.third.weaver.model.request.value.WorkflowRequestTableRecord;
import com.spes.sop.user.service.user.model.bo.UserBO;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 组合品Controller层转换器
 * 负责Controller层模型与Service层模型之间的转换
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public class CombinationDTOConverter {

    /**
     * 将CombinationCreateRequest转换为CombinationCreateCommand
     */
    public static CombinationCreateCommand toCreateCommand(@NotNull CombinationCreateRequest request,
                                                           @NotNull Long operatorId,
                                                           @NotNull SpuBO spuInfo, @NotNull String combinationCode) {
        // 转换SKU关联项
        List<CombinationSkuRelationCreateCommand> skuItems = request.getSkuIds().stream()
                .map(item -> CombinationSkuRelationCreateCommand.builder()
                        .skuId(item.getSkuId())
                        .amount(item.getSkuNum())
                        .main(item.getMain())
                        .gift(item.getGift())
                        .build())
                .collect(Collectors.toList());

        return CombinationCreateCommand.builder()
                .name(request.getCombinationName())
                .combinationCode(combinationCode)
                .description(request.getDescription())
                .brandId(spuInfo.getBrandId())
                .seriesId(spuInfo.getSeriesId())
                .categoryId(spuInfo.getCategoryId())
                .firstClassification(spuInfo.getFirstClassification())
                .secondClassification(spuInfo.getSecondClassification())
                .thirdClassification(spuInfo.getThirdClassification())
                .fourthClassification(spuInfo.getFourthClassification())
                .channelId(spuInfo.getChannelId())
                .skuItems(skuItems)
                .operatorId(operatorId)
                .build();
    }

    public static CombinationBOPageQuery toServiceQuery(@NotNull CombinationPageQuery request) {
        return CombinationBOPageQuery.builder()
                .combinationNameSearch(request.getCombinationNameSearch())
                .combinationCode(request.getCombinationCode())
                .firstClassification(ObjectUtil.isNull(request.getFirstClassification()) ? null :
                        Collections.singletonList(request.getFirstClassification()))
                .secondClassification(ObjectUtil.isNull(request.getSecondClassification()) ? null :
                        Collections.singletonList(request.getSecondClassification()))
                .thirdClassification(ObjectUtil.isNull(request.getThirdClassification()) ? null :
                        Collections.singletonList(request.getThirdClassification()))
                .statuses(CollUtil.isEmpty(request.getStatuses()) ? null :
                        request.getStatuses().stream().map(GoodsStatusEnum::getByName).collect(Collectors.toList()))
                .seriesId(request.getSeriesId())
                .categoryIds(request.getCategoryIds())
                .gift(request.getGift())
                .packageCard(request.getPackageCard())
                .pager(new BasePager.Pager(request.getPageNum(), request.getPageSize()))
                .build();
    }

    public static CombinationDetailDTO toDetailDTO(CombinationDetailBO combination,
                                                   List<SkuListBO> skus,
                                                   List<UserBO> users,
                                                   List<GoodsBrandBO> brands,
                                                   List<GoodsCategoryBO> categories,
                                                   List<GoodsChannelBO> channels,
                                                   List<GoodsSeriesBO> series,
                                                   List<GoodsClassificationBO> classifications) {
        Map<Long, String> classificationsMap = CollUtil.isEmpty(classifications) ? Maps.newHashMap() :
                classifications.stream().collect(Collectors.toMap(GoodsClassificationBO::getId,
                        GoodsClassificationBO::getName));
        Map<Long, String> usersMap = CollUtil.isEmpty(users) ? Maps.newHashMap() :
                users.stream().collect(Collectors.toMap(UserBO::getId, UserBO::getUsername));
        GoodsBrandBO brandBO = CollUtil.isEmpty(brands) ? null : brands.get(0);
        GoodsCategoryBO categoryBO = CollUtil.isEmpty(categories) ? null : categories.get(0);
        GoodsChannelBO channelBO = CollUtil.isEmpty(channels) ? null : channels.get(0);
        GoodsSeriesBO seriesBO = CollUtil.isEmpty(series) ? null : series.get(0);

        return CombinationDetailDTO.builder()
                .id(combination.getId())
                .combinationCode(combination.getCombinationCode())
                .combinationName(combination.getName())
                .description(combination.getDescription())
                .brand(ObjectUtil.isNull(brandBO) ? null : brandBO.getName())
                .firstClassification(classificationsMap.getOrDefault(combination.getFirstClassification(), null))
                .secondClassification(classificationsMap.getOrDefault(combination.getSecondClassification(), null))
                .thirdClassification(classificationsMap.getOrDefault(combination.getThirdClassification(), null))
                .fourthClassification(classificationsMap.getOrDefault(combination.getFourthClassification(), null))
                .series(ObjectUtil.isNull(seriesBO) ? null : seriesBO.getName())
                .category(ObjectUtil.isNull(categoryBO) ? null : categoryBO.getName())
                .channel(ObjectUtil.isNull(channelBO) ? null : channelBO.getName())
                .skuItems(buildSkuItemsByVersionDTO(combination.getSkuRelations(), skus))
                .status(combination.getStatus().name())
                .statusDesc(combination.getStatus().getDesc())
                .creatorId(combination.getCreatorId())
                .updaterId(combination.getUpdaterId())
                .creatorName(usersMap.getOrDefault(combination.getCreatorId(), null))
                .updaterName(usersMap.getOrDefault(combination.getUpdaterId(), null))
                .createTime(combination.getCreateTime())
                .updateTime(combination.getUpdateTime())
                .build();

    }

    private static Map<Integer, List<CombinationSkuItemDTO>> buildSkuItemsByVersionDTO(List<CombinationSkuRelationBO> skuRelations, List<SkuListBO> skus) {
        Map<Long, SkuListBO> skusMap = CollUtil.isEmpty(skus) ? Maps.newHashMap() :
                skus.stream().collect(Collectors.toMap(SkuListBO::getId, sku -> sku));
        return skuRelations.stream().map(a -> CombinationSkuItemDTO.builder()
                        .id(a.getId())
                        .skuCode(skusMap.get(a.getSkuId()).getSkuCode())
                        .skuName(skusMap.get(a.getSkuId()).getSkuName())
                        .barCode(skusMap.get(a.getSkuId()).getBarCode())
                        .spec(skusMap.get(a.getSkuId()).getSpecification())
                        .skuNum(a.getAmount())
                        .main(a.getMain())
                        .fairPrice(skusMap.get(a.getSkuId()).getFairPrice())
                        .effective(a.getEffective())
                        .effectiveTime(a.getEffectiveTime())
                        .expirationTime(a.getExpirationTime())
                        .version(a.getCombinationVersion())
                        .build())
                .collect(Collectors.groupingBy(CombinationSkuItemDTO::getVersion));
    }

    /**
     * 将CombinationUpdateRequest转换为CombinationUpdateCommand
     *
     * @param request    更新请求
     * @param operatorId 操作人ID
     * @return 服务层更新命令
     */
    public static CombinationUpdateCommand toUpdateCommand(@NotNull CombinationUpdateRequest request,
                                                           @NotNull Long operatorId) {
        if (request == null) {
            return null;
        }

        // 转换SKU关联项
        List<CombinationSkuRelationUpdateCommand> skuItems = null;
        if (CollUtil.isNotEmpty(request.getSkuItems())) {
            Date effectTime = new Date();
            skuItems = request.getSkuItems().stream()
                    .map(item -> CombinationSkuRelationUpdateCommand.builder()
                            .skuId(item.getSkuId())
                            .amount(item.getSkuNum())
                            .main(item.getMain())
                            .gift(item.getGift())
                            .effectiveTime(effectTime)
                            .build())
                    .collect(Collectors.toList());
        }

        return CombinationUpdateCommand.builder()
                .id(request.getId())
                .name(request.getCombinationName())
                .description(request.getDescription())
                .skuItems(skuItems)
                .operatorId(operatorId)
                .build();
    }

    public static Map<String, Object> convert2workflowRequest(CombinationCreateCommand command, JwtUserDetails user,
                                                              List<SkuListBO> skuInfos) {
        Map<String, Object> params = new HashMap<>();
        params.put("requestName", generateComboRequestName(command));
        params.put("mainData", getMainData(user));
        params.put("workflowId", WeaverWorkflowConstant.WorkflowId.COMBINATION_APPROVAL_WORKFLOW_ID);
        params.put("detailData", getComboDetailData(command, skuInfos));
        return params;
    }

    private static String getComboDetailData(CombinationCreateCommand command, List<SkuListBO> skuInfos) {
        if (CollUtil.isEmpty(command.getSkuItems())) {
            return JSONObject.toJSONString(new ArrayList<>());
        }
        Map<Long, SkuListBO> skus = skuInfos.stream().collect(Collectors.toMap(SkuListBO::getId, Function.identity(),
                (a, b) -> b));
        List<WorkflowRequestTable> detailData = new ArrayList<>();
        WorkflowRequestTable relationTable = new WorkflowRequestTable();
        relationTable.setTableDBName("formtable_main_248_dt1");
        relationTable.setWorkflowRequestTableRecords(new ArrayList<>(command.getSkuItems().size()));
        for (int i = 0; i < command.getSkuItems().size(); i++) {
            CombinationSkuRelationCreateCommand relation = command.getSkuItems().get(i);
            SkuListBO skuInfo = skus.get(relation.getSkuId());
            if (ObjectUtil.isNull(skuInfo)) {
                continue;
            }
            WorkflowRequestTableRecord businessTableRecord = new WorkflowRequestTableRecord();
            businessTableRecord.setRecordOrder(0);
            businessTableRecord.setWorkflowRequestTableFields(buildTableFields(command, relation, skuInfo));
            relationTable.getWorkflowRequestTableRecords().add(businessTableRecord);
        }
        detailData.add(relationTable);
        return JSONObject.toJSONString(detailData);
    }

    private static List<WorkflowRequestTableField> buildTableFields(CombinationCreateCommand createCommand,
                                                                    CombinationSkuRelationCreateCommand command,
                                                                    SkuListBO skuInfo) {
        List<WorkflowRequestTableField> tableFields = new ArrayList<>();
        tableFields.add(new WorkflowRequestTableField("skuCode", skuInfo.getSkuCode()));
        tableFields.add(new WorkflowRequestTableField("skuName", skuInfo.getSkuName()));
        tableFields.add(new WorkflowRequestTableField("barCode", skuInfo.getBarCode()));
        tableFields.add(new WorkflowRequestTableField("amount", String.valueOf(command.getAmount())));
        tableFields.add(new WorkflowRequestTableField("combinationCode", createCommand.getCombinationCode()));
        tableFields.add(new WorkflowRequestTableField("combinationName", createCommand.getName()));
        tableFields.add(new WorkflowRequestTableField("description", createCommand.getDescription()));
        tableFields.add(new WorkflowRequestTableField("main", command.getMain() ? "1" : "0"));
        tableFields.add(new WorkflowRequestTableField("gift", command.getGift() ? "1" : "0"));
        return tableFields;
    }


    private static String getMainData(JwtUserDetails user) {
        List<WorkflowRequestTableField> mainData = new ArrayList<>();
        //附件上传字段
        WorkflowRequestTableField field1 = new WorkflowRequestTableField();
        field1.setFieldName("createTime");
        field1.setFieldValue(DateUtil.format(new Date(), "yyyy-MM-dd"));
        mainData.add(field1);

        //单行文本字段
        WorkflowRequestTableField field2 = new WorkflowRequestTableField();
        field2.setFieldName("departmentName");
        field2.setFieldValue(user.getDepartment());
        mainData.add(field2);

        WorkflowRequestTableField field3 = new WorkflowRequestTableField();
        field3.setFieldName("creatorName");
        field3.setFieldValue(String.valueOf(user.getWeaverId()));
        mainData.add(field3);

        return JSONObject.toJSONString(mainData);
    }

    private static String generateComboRequestName(CombinationCreateCommand command) {
        String today = DateUtil.format(new Date(), "yyyy-MM-dd");
        return String.format("组合品审批-%s-%s", command.getName(), today);
    }


    public static List<CombinationPageDTO> toListDTO(List<CombinationListBO> combinations,
                                                     List<SkuListBO> skus,
                                                     List<GoodsCategoryBO> categories,
                                                     List<GoodsChannelBO> channels,
                                                     List<GoodsSeriesBO> series,
                                                     List<GoodsClassificationBO> classifications,
                                                     List<UserBO> users,
                                                     List<GoodsBrandBO> brands) {
        Map<Long, String> skuMap = CollUtil.isNotEmpty(skus) ?
                skus.stream().collect(Collectors.toMap(SkuListBO::getId, SkuListBO::getSkuName)) : Maps.newHashMap();
        Map<Long, String> seriesMap = CollUtil.isNotEmpty(series) ?
                series.stream().collect(Collectors.toMap(GoodsSeriesBO::getId, GoodsSeriesBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> classificationMap = CollUtil.isNotEmpty(classifications) ?
                classifications.stream().collect(Collectors.toMap(GoodsClassificationBO::getId,
                        GoodsClassificationBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> categoryMap = CollUtil.isNotEmpty(categories) ?
                categories.stream().collect(Collectors.toMap(GoodsCategoryBO::getId, GoodsCategoryBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> channelMap = CollUtil.isNotEmpty(channels) ?
                channels.stream().collect(Collectors.toMap(GoodsChannelBO::getId, GoodsChannelBO::getName)) :
                Maps.newHashMap();
        Map<Long, String> userMap = CollUtil.isNotEmpty(users) ?
                users.stream().collect(Collectors.toMap(UserBO::getId, UserBO::getUsername)) :
                Maps.newHashMap();
        Map<Long, String> brandMap = CollUtil.isNotEmpty(brands) ?
                brands.stream().collect(Collectors.toMap(GoodsBrandBO::getId, GoodsBrandBO::getName)) :
                Maps.newHashMap();
        return combinations.stream()
                .map(combination -> toPageDTO(combination, skuMap, seriesMap, classificationMap, categoryMap,
                        channelMap, userMap, brandMap))
                .collect(Collectors.toList());
    }

    private static CombinationPageDTO toPageDTO(CombinationListBO combination,
                                                Map<Long, String> skuMap,
                                                Map<Long, String> seriesMap,
                                                Map<Long, String> classificationMap,
                                                Map<Long, String> categoryMap,
                                                Map<Long, String> channelMap,
                                                Map<Long, String> userMap,
                                                Map<Long, String> brandMap) {
        return CombinationPageDTO.builder()
                .id(combination.getId())
                .combinationName(combination.getName())
                .combinationCode(combination.getCombinationCode())
                .description(combination.getDescription())
                .status(combination.getStatus().name())
                .statusDesc(combination.getStatus().getDesc())
                .skuNames((CollUtil.isNotEmpty(combination.getSkuIds()) && CollUtil.isNotEmpty(skuMap)) ?
                        combination.getSkuIds().stream().map(skuMap::get).collect(Collectors.toList()) :
                        Lists.newArrayList())
                .series(ObjectUtil.isNotNull(combination.getSeriesId()) && CollUtil.isNotEmpty(seriesMap) ?
                        seriesMap.get(combination.getSeriesId()) : null)
                .category((ObjectUtil.isNotNull(combination.getCategoryId()) && CollUtil.isNotEmpty(categoryMap)) ?
                        categoryMap.get(combination.getCategoryId()) : null)
                .channel((ObjectUtil.isNotNull(combination.getChannelId()) && CollUtil.isNotEmpty(channelMap)) ?
                        channelMap.get(combination.getChannelId()) : null)
                .firstClassification((ObjectUtil.isNotNull(combination.getFirstClassification()) && CollUtil.isNotEmpty(classificationMap)) ?
                        classificationMap.get(combination.getFirstClassification()) : null)
                .secondClassification((ObjectUtil.isNotNull(combination.getSecondClassification()) && CollUtil.isNotEmpty(classificationMap)) ?
                        classificationMap.get(combination.getSecondClassification()) : null)
                .thirdClassification((ObjectUtil.isNotNull(combination.getThirdClassification()) && CollUtil.isNotEmpty(classificationMap)) ?
                        classificationMap.get(combination.getThirdClassification()) : null)
                .fourthClassification((ObjectUtil.isNotNull(combination.getFourthClassification()) && CollUtil.isNotEmpty(classificationMap)) ?
                        classificationMap.get(combination.getFourthClassification()) : null)
                .brand(ObjectUtil.isNotNull(combination.getBrandId()) && CollUtil.isNotEmpty(brandMap) ?
                        brandMap.get(combination.getBrandId()) : null)
                .createTime(combination.getCreateTime())
                .updateTime(combination.getUpdateTime())
                .creatorId(combination.getCreatorId())
                .updaterId(combination.getUpdaterId())
                .creatorName(CollUtil.isNotEmpty(userMap) ? userMap.get(combination.getCreatorId()) : null)
                .updaterName(CollUtil.isNotEmpty(userMap) ? userMap.get(combination.getUpdaterId()) : null)
                .build();
    }
}