package com.spes.sop.controller.goods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.spes.sop.common.page.PageResult;
import com.spes.sop.common.result.Result;
import com.spes.sop.controller.goods.model.convert.SkuVOConvert;
import com.spes.sop.controller.goods.model.query.SkuPageQuery;
import com.spes.sop.controller.goods.model.request.SkuApprovalRequest;
import com.spes.sop.controller.goods.model.request.SkuCreateRequest;
import com.spes.sop.controller.goods.model.request.SkuUpdateRequest;
import com.spes.sop.controller.goods.model.vo.SkuDetailVO;
import com.spes.sop.controller.goods.model.vo.SkuListVO;
import com.spes.sop.support.goods.SkuSupport;
import com.spes.sop.support.goods.SpuSupport;
import com.spes.sop.support.goods.model.dto.SkuDetailDTO;
import com.spes.sop.support.goods.model.dto.SkuListDTO;
import com.spes.sop.support.goods.model.dto.SpuDetailDTO;
import com.spes.sop.support.goods.model.dto.SpuPageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SKU规格管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/sku")
public class SkuController {

    private final SkuSupport skuSupport;
    private final SpuSupport spuSupport;

    /**
     * SKU分页查询
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/page")
    public Result<PageResult<SkuListVO>> page(@Validated @RequestBody SkuPageQuery request) {
        //sku 信息
        PageResult<SkuListDTO> page = skuSupport.page(request);
        if (CollUtil.isEmpty(page.getRecords())) {
            return Result.success(PageResult.of(CollUtil.newArrayList(), page.getTotal(), page.getPageNum(),
                    page.getPageSize()));
        }
        //sku 对应的spu信息
        List<Long> spuIds = page.getRecords().stream().map(a -> a.getSpuId()).collect(Collectors.toList());
        List<SpuPageDTO> spuInfos = spuSupport.getByIds(spuIds);
        //组装
        List<SkuListVO> result = SkuVOConvert.skuListConvert(page.getRecords(), spuInfos);
        return Result.success(PageResult.of(result, page.getTotal(), page.getPageNum(), page.getPageSize()));
    }

    /**
     * 根据ID查询SKU详情
     *
     * @param id SKU ID
     * @return SKU详情
     */
    @GetMapping("/get/{id}")
    public Result<SkuDetailVO> getById(@PathVariable Long id) {
        log.info("根据ID查询SKU详情，ID：{}", id);
        SkuDetailDTO skuDetail = skuSupport.getById(id);
        if (ObjectUtil.isNull(skuDetail)) {
            return Result.error("SKU不存在，ID：" + id);
        }
        SpuDetailDTO spuDetail = spuSupport.getById(skuDetail.getSpuId());
        SkuDetailVO result = SkuVOConvert.skuDetailConvert(skuDetail, spuDetail);
        return Result.success(result);
    }

    /**
     * 新增SKU
     *
     * @param request 新增请求
     * @return 操作结果
     */
    @PostMapping("create")
    public Result<Void> create(@Valid @RequestBody SkuCreateRequest request) {
        log.info("新增SKU，请求参数：{}", request);
        skuSupport.create(request);
        return Result.success();
    }

    /**
     * 编辑 sku
     */
    @PostMapping("/update")
    public Result<Void> update(@Valid @RequestBody SkuUpdateRequest request) {
        log.info("更新SKU，请求参数：{}", request);
        skuSupport.update(request);
        return Result.success();
    }

    /**
     * 删除SKU
     *
     * @param id SKU ID
     * @return 操作结果
     */
    @PostMapping("/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        log.info("删除SKU，ID：{}", id);
        skuSupport.delete(id);
        return Result.success();
    }

    /**
     * 发起审批
     */
    @PostMapping("/approve")
    public Result<Void> approve(@RequestBody @Validated SkuApprovalRequest request) {
        log.info("发起SKU审批，SKU IDs：{}，附件数量：{}", request.getIds(),
                request.getAttachments() != null ? request.getAttachments().size() : 0);
        skuSupport.approve(request);
        return Result.success();
    }
}
